# 嵌入模型配置说明

## 概述

系统现在支持两种嵌入模型：
1. **火山引擎 doubao-embedding** (推荐)
2. **sentence-transformers** (备用)

## 配置方式

### 1. 火山引擎 doubao-embedding (推荐)

#### 安装依赖
```bash
pip install volcenginesdkarkruntime
```

#### 配置环境变量
在 `backend/.env` 文件中添加火山引擎API配置：
```env
# 火山引擎API配置
VOLC_ACCESSKEY=your_access_key
VOLC_SECRETKEY=your_secret_key
VOLC_REGION=cn-beijing
```

#### 配置说明
- 系统会自动使用火山引擎的 `doubao-embedding-text-240715` 模型
- 嵌入维度：根据模型自动检测
- 支持批量嵌入，性能更好

### 2. sentence-transformers (备用)

#### 自动降级
如果火山引擎配置失败，系统会自动降级到 sentence-transformers：
- 模型：`all-MiniLM-L6-v2`
- 嵌入维度：384
- 本地运行，无需API密钥

## 配置选项

在 `backend/config.py` 中可以调整以下配置：

```python
VECTOR_DB_CONFIG = {
    'use_vector_db': True,  # 是否使用向量数据库
    'embedding_model': 'doubao-embedding-text-240715',  # 嵌入模型名称
    'use_volcengine_embedding': True,  # 是否优先使用火山引擎嵌入
    'similarity_threshold': 0.4,  # 相似度阈值
    'max_memories_per_query': 5,  # 每次查询返回的最大记忆数
}
```

## 使用示例

### 基本使用
```python
from services.embedding_service import get_embedding_service, create_embeddings

# 获取嵌入服务
service = get_embedding_service()

# 生成单个文本嵌入
embedding = service.encode("我喜欢画画")

# 生成批量文本嵌入
embeddings = service.encode(["文本1", "文本2", "文本3"])

# 便捷函数
embedding = create_embeddings("测试文本")
```

### 获取模型信息
```python
model_info = service.get_model_info()
print(f"提供商: {model_info['provider']}")
print(f"模型: {model_info['model_name']}")
print(f"维度: {model_info['embedding_dim']}")
```

## 性能对比

| 特性 | 火山引擎 doubao-embedding | sentence-transformers |
|------|---------------------------|----------------------|
| 嵌入质量 | 高 (专为中文优化) | 中等 |
| 处理速度 | 快 (云端API) | 中等 (本地计算) |
| 成本 | 按调用计费 | 免费 |
| 网络依赖 | 需要 | 不需要 |
| 配置复杂度 | 需要API密钥 | 无需配置 |

## 故障排除

### 火山引擎连接失败
1. 检查API密钥配置是否正确
2. 确认网络连接正常
3. 查看日志中的具体错误信息

### sentence-transformers加载失败
1. 确认已安装依赖：`pip install sentence-transformers`
2. 检查网络连接（首次使用需要下载模型）
3. 确认磁盘空间充足

### 嵌入维度不匹配
1. 清除向量数据库：删除 `chroma_db` 目录
2. 重启系统，让向量数据库重新初始化

## 监控和日志

系统会记录以下信息：
- 嵌入服务初始化状态
- 模型切换情况
- 嵌入生成性能
- 错误和警告信息

查看日志：
```bash
# 启动时的初始化日志
tail -f logs/app.log | grep "嵌入"

# 实时监控嵌入生成
tail -f logs/app.log | grep "Batches"
```

## 最佳实践

1. **生产环境**：推荐使用火山引擎 doubao-embedding
2. **开发测试**：可以使用 sentence-transformers
3. **批量处理**：尽量使用批量嵌入接口提高性能
4. **错误处理**：系统已内置降级机制，无需额外处理
5. **监控**：定期检查嵌入服务状态和性能指标
