"""
Chat Completions Client - 统一的LLM API调用客户端
支持火山引擎和其他LLM服务提供商
"""

import json
import requests
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)


class ChatCompletionsClient(ABC):
    """Chat Completions客户端抽象基类"""
    
    @abstractmethod
    def chat_completions(
        self,
        messages: List[Dict[str, str]],
        model: str = None,
        temperature: float = 0.7,
        max_tokens: int = 1000,
        **kwargs
    ) -> Dict[str, Any]:
        """
        发送聊天完成请求
        
        Args:
            messages: 消息列表
            model: 模型名称
            temperature: 温度参数
            max_tokens: 最大token数
            **kwargs: 其他参数
            
        Returns:
            API响应结果
        """
        pass


class VolcengineClient(ChatCompletionsClient):
    """火山引擎LLM客户端"""
    
    def __init__(self, access_key: str, secret_key: str, region: str = "cn-beijing"):
        self.access_key = access_key
        self.secret_key = secret_key
        self.region = region
        self.base_url = "https://ark.cn-beijing.volces.com/api/v3"
    
    def chat_completions(
        self,
        messages: List[Dict[str, str]],
        model: str = "doubao-lite-4k",
        temperature: float = 0.7,
        max_tokens: int = 1000,
        **kwargs
    ) -> Dict[str, Any]:
        """
        调用火山引擎Chat Completions API
        """
        request_id = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        
        try:
            # 构建请求数据
            data = {
                "model": model,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": max_tokens,
                "stream": False,
                **kwargs
            }
            
            # 记录请求日志
            logger.info(f"🌐 火山引擎API请求 [{request_id}]")
            logger.info(f"📋 请求数据: model={model}, messages={len(messages)}条, temperature={temperature}")
            logger.info(f"💬 最后一条消息: {messages[-1]['content'][:100]}..." if messages else "无消息")
            
            # 构建请求头
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self._get_access_token()}"
            }
            
            # 发送请求
            logger.info(f"🚀 发送请求到: {self.base_url}/chat/completions")
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=data,
                timeout=30
            )
            
            # 处理响应
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content'].strip()
                
                logger.info(f"✅ 火山引擎API响应成功 [{request_id}]")
                logger.info(f"📝 响应内容: {content}")
                logger.info(f"📊 响应统计: {len(content)} 字符")
                
                return {
                    "success": True,
                    "content": content,
                    "raw_response": result,
                    "request_id": request_id
                }
            else:
                logger.error(f"❌ 火山引擎API错误 [{request_id}]")
                logger.error(f"状态码: {response.status_code}")
                logger.error(f"错误信息: {response.text}")
                
                return {
                    "success": False,
                    "error": f"API错误: {response.status_code}",
                    "error_detail": response.text,
                    "request_id": request_id
                }
                
        except requests.exceptions.Timeout:
            logger.error(f"⏰ 火山引擎API超时 [{request_id}]")
            return {
                "success": False,
                "error": "请求超时",
                "request_id": request_id
            }
        except requests.exceptions.ConnectionError:
            logger.error(f"🔌 火山引擎API连接错误 [{request_id}]")
            return {
                "success": False,
                "error": "连接错误",
                "request_id": request_id
            }
        except Exception as e:
            logger.error(f"💥 火山引擎API异常 [{request_id}]: {str(e)}")
            return {
                "success": False,
                "error": f"未知错误: {str(e)}",
                "request_id": request_id
            }
    
    def _get_access_token(self) -> str:
        """
        获取访问令牌
        注意：这里需要根据火山引擎的实际认证方式进行调整
        """
        return self.access_key


class MockClient(ChatCompletionsClient):
    """模拟LLM客户端 - 用于测试和演示"""
    
    def __init__(self):
        self.responses = {
            'greeting': [
                "你好！我是小雨，很高兴认识你～作为一名心理咨询师，我希望能够陪伴你度过美好的时光。",
                "嗨！我是小雨，今天过得怎么样呢？有什么想和我分享的吗？",
                "你好呀！我刚结束了一个咨询个案，现在有时间陪你聊天了～"
            ],
            'positive': [
                "听起来你心情不错呢！能分享一下是什么让你这么开心吗？",
                "你的积极态度真的很感染人！作为心理咨询师，我觉得保持乐观很重要。",
                "太好了！看到你这么开心，我也跟着高兴起来了～"
            ],
            'sad': [
                "我能感受到你现在的情绪，想聊聊发生了什么吗？我会认真倾听的。",
                "每个人都会有低落的时候，这很正常。作为心理咨询师，我想告诉你，你并不孤单。",
                "我理解你现在的感受。有时候，把心里的话说出来会让人感觉好一些。"
            ],
            'work': [
                "工作确实不容易呢。我今天也遇到了一个比较有挑战性的案例，不过帮助别人总是让我很有成就感。",
                "说到工作，我刚刚在学习一些新的心理治疗技术，希望能更好地帮助来访者。",
                "工作压力大的时候，记得要好好照顾自己哦。我平时会通过散步来放松。"
            ],
            'personal': [
                "谢谢你愿意和我分享这些！我会记住的，这对我了解你很重要。",
                "听你说这些，我觉得我们越来越了解彼此了呢～",
                "你的经历很有趣！我也想和你分享一些我的想法。"
            ],
            'default': [
                "嗯嗯，我在认真听你说话。能再详细说说吗？",
                "这个话题很有意思呢！你是怎么想的？",
                "我理解你的想法。作为心理咨询师，我觉得每个人的观点都很珍贵。",
                "说得很有道理！我也有类似的感受。",
                "你提到的这点让我想到了一些专业知识，不过现在更想听听你的想法。"
            ]
        }
    
    def chat_completions(
        self,
        messages: List[Dict[str, str]],
        model: str = "mock-model",
        temperature: float = 0.7,
        max_tokens: int = 1000,
        **kwargs
    ) -> Dict[str, Any]:
        """
        模拟Chat Completions API调用
        """
        request_id = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        
        try:
            # 记录请求日志
            logger.info(f"🤖 模拟API请求 [{request_id}]")
            logger.info(f"📋 请求数据: model={model}, messages={len(messages)}条, temperature={temperature}")
            
            # 获取用户消息
            user_message = ""
            for msg in reversed(messages):
                if msg.get('role') == 'user':
                    user_message = msg.get('content', '')
                    break
            
            logger.info(f"💬 用户消息: {user_message}")
            
            # 根据消息内容选择回复类型
            message_lower = user_message.lower()
            
            if any(word in message_lower for word in ['你好', 'hi', 'hello', '初次见面']):
                response_type = 'greeting'
            elif any(word in message_lower for word in ['开心', '高兴', '快乐', '棒', '好']):
                response_type = 'positive'
            elif any(word in message_lower for word in ['难过', '伤心', '沮丧', '不开心', '郁闷']):
                response_type = 'sad'
            elif any(word in message_lower for word in ['工作', '上班', '同事', '老板', '压力']):
                response_type = 'work'
            elif any(word in message_lower for word in ['我', '我的', '我觉得', '我想']):
                response_type = 'personal'
            else:
                response_type = 'default'
            
            # 随机选择回复
            import random
            responses = self.responses[response_type]
            content = random.choice(responses)
            
            logger.info(f"✅ 模拟API响应成功 [{request_id}]")
            logger.info(f"🎯 选择回复类型: {response_type}")
            logger.info(f"📝 响应内容: {content}")
            logger.info(f"📊 响应统计: {len(content)} 字符")
            
            return {
                "success": True,
                "content": content,
                "response_type": response_type,
                "request_id": request_id
            }
            
        except Exception as e:
            logger.error(f"💥 模拟API异常 [{request_id}]: {str(e)}")
            return {
                "success": False,
                "error": f"模拟API错误: {str(e)}",
                "request_id": request_id
            }


def create_client(provider: str = "volcengine", **config) -> ChatCompletionsClient:
    """
    创建Chat Completions客户端
    
    Args:
        provider: 服务提供商 ("volcengine" 或 "mock")
        **config: 配置参数
        
    Returns:
        客户端实例
    """
    if provider == "volcengine":
        return VolcengineClient(
            access_key=config.get('access_key', ''),
            secret_key=config.get('secret_key', ''),
            region=config.get('region', 'cn-beijing')
        )
    elif provider == "mock":
        return MockClient()
    else:
        raise ValueError(f"不支持的服务提供商: {provider}")
