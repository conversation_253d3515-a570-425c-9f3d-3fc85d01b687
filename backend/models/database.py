import sqlite3
import json
from datetime import datetime
from typing import List, Dict, Optional, Tuple
from config import Config

class DatabaseManager:
    def __init__(self):
        self.db_path = Config.DATABASE_PATH
        self.init_database()

    def init_database(self):
        """初始化数据库表"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # 用户信息表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT UNIQUE NOT NULL,
                    username TEXT UNIQUE,
                    password_hash TEXT,
                    nickname TEXT,
                    email TEXT,
                    status TEXT DEFAULT 'active',
                    name TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_active TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 用户会话表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_token TEXT UNIQUE NOT NULL,
                    user_id TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expires_at TIMESTAMP NOT NULL,
                    is_active BOOLEAN DEFAULT 1,
                    FOREIGN KEY (user_id) REFERENCES users (user_id)
                )
            ''')

            # 对话记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS conversations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    message TEXT NOT NULL,
                    sender TEXT NOT NULL,  -- 'user' or 'assistant'
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    emotion_score REAL DEFAULT 0.0,
                    FOREIGN KEY (user_id) REFERENCES users (user_id)
                )
            ''')

            # 用户记忆表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS memories (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    memory_type TEXT NOT NULL,  -- 'personal', 'preference', 'experience'
                    content TEXT NOT NULL,
                    importance REAL DEFAULT 1.0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_recalled TIMESTAMP,
                    recall_count INTEGER DEFAULT 0,
                    FOREIGN KEY (user_id) REFERENCES users (user_id)
                )
            ''')

            # 好感度记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS affection_levels (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    level INTEGER NOT NULL,
                    change_reason TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (user_id)
                )
            ''')

            # 虚拟人状态表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS persona_states (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date DATE NOT NULL,
                    current_activity TEXT,
                    mood TEXT,
                    work_content TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            conn.commit()

    def get_user_or_create(self, user_id: str, name: str = None) -> Dict:
        """获取或创建用户"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # 尝试获取用户
            cursor.execute('SELECT * FROM users WHERE user_id = ?', (user_id,))
            user = cursor.fetchone()

            if not user:
                # 创建新用户
                cursor.execute(
                    'INSERT INTO users (user_id, name) VALUES (?, ?)',
                    (user_id, name or f'用户{user_id[:8]}')
                )
                conn.commit()

                # 初始化好感度
                cursor.execute(
                    'INSERT INTO affection_levels (user_id, level, change_reason) VALUES (?, ?, ?)',
                    (user_id, Config.AFFECTION_CONFIG['initial_level'], '初始好感度')
                )
                conn.commit()

                cursor.execute('SELECT * FROM users WHERE user_id = ?', (user_id,))
                user = cursor.fetchone()

            return {
                'id': user[0],
                'user_id': user[1],
                'name': user[2],
                'created_at': user[3],
                'last_active': user[4]
            }

    def save_conversation(self, user_id: str, message: str, sender: str, emotion_score: float = 0.0):
        """保存对话记录"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute(
                'INSERT INTO conversations (user_id, message, sender, emotion_score) VALUES (?, ?, ?, ?)',
                (user_id, message, sender, emotion_score)
            )
            conn.commit()

    def get_recent_conversations(self, user_id: str, limit: int = 10) -> List[Dict]:
        """获取最近的对话记录"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute(
                'SELECT message, sender, timestamp FROM conversations WHERE user_id = ? ORDER BY timestamp DESC LIMIT ?',
                (user_id, limit)
            )
            conversations = cursor.fetchall()

            return [
                {
                    'message': conv[0],
                    'sender': conv[1],
                    'timestamp': conv[2]
                }
                for conv in reversed(conversations)  # 按时间正序返回
            ]

    def save_memory(self, user_id: str, memory_type: str, content: str, importance: float = 1.0):
        """保存用户记忆"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute(
                'INSERT INTO memories (user_id, memory_type, content, importance) VALUES (?, ?, ?, ?)',
                (user_id, memory_type, content, importance)
            )
            conn.commit()

    def get_relevant_memories(self, user_id: str, keywords: List[str] = None, limit: int = 5) -> List[Dict]:
        """获取相关记忆"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            if keywords:
                # 简单的关键词匹配
                keyword_conditions = ' OR '.join(['content LIKE ?' for _ in keywords])
                query = f'''
                    SELECT content, memory_type, importance, created_at
                    FROM memories
                    WHERE user_id = ? AND ({keyword_conditions})
                    ORDER BY importance DESC, created_at DESC
                    LIMIT ?
                '''
                params = [user_id] + [f'%{kw}%' for kw in keywords] + [limit]
            else:
                query = '''
                    SELECT content, memory_type, importance, created_at
                    FROM memories
                    WHERE user_id = ?
                    ORDER BY importance DESC, created_at DESC
                    LIMIT ?
                '''
                params = [user_id, limit]

            cursor.execute(query, params)
            memories = cursor.fetchall()

            return [
                {
                    'content': mem[0],
                    'type': mem[1],
                    'importance': mem[2],
                    'created_at': mem[3]
                }
                for mem in memories
            ]

    def get_current_affection(self, user_id: str) -> int:
        """获取当前好感度"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute(
                'SELECT level FROM affection_levels WHERE user_id = ? ORDER BY timestamp DESC LIMIT 1',
                (user_id,)
            )
            result = cursor.fetchone()
            return result[0] if result else Config.AFFECTION_CONFIG['initial_level']

    def update_affection(self, user_id: str, change: int, reason: str):
        """更新好感度"""
        current_level = self.get_current_affection(user_id)
        new_level = max(
            Config.AFFECTION_CONFIG['min_level'],
            min(Config.AFFECTION_CONFIG['max_level'], current_level + change)
        )

        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute(
                'INSERT INTO affection_levels (user_id, level, change_reason) VALUES (?, ?, ?)',
                (user_id, new_level, reason)
            )
            conn.commit()

        return new_level

    # 新增用户管理方法
    def create_user(self, user_data: Dict):
        """创建新用户"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO users (user_id, username, password_hash, nickname, email, status, created_at, last_active)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                user_data['user_id'],
                user_data.get('username'),
                user_data.get('password_hash'),
                user_data.get('nickname'),
                user_data.get('email'),
                user_data.get('status', 'active'),
                user_data.get('created_at'),
                user_data.get('last_active')
            ))
            conn.commit()

    def get_user_by_username(self, username: str) -> Optional[Dict]:
        """根据用户名获取用户"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM users WHERE username = ?', (username,))
            user = cursor.fetchone()

            if user:
                return {
                    'id': user[0],
                    'user_id': user[1],
                    'username': user[2],
                    'password_hash': user[3],
                    'nickname': user[4],
                    'email': user[5],
                    'status': user[6],
                    'name': user[7],
                    'created_at': user[8],
                    'last_active': user[9]
                }
            return None

    def get_user_by_id(self, user_id: str) -> Optional[Dict]:
        """根据用户ID获取用户"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM users WHERE user_id = ?', (user_id,))
            user = cursor.fetchone()

            if user:
                return {
                    'id': user[0],
                    'user_id': user[1],
                    'username': user[2],
                    'password_hash': user[3],
                    'nickname': user[4],
                    'email': user[5],
                    'status': user[6],
                    'name': user[7],
                    'created_at': user[8],
                    'last_active': user[9]
                }
            return None

    def update_user(self, user_id: str, update_data: Dict) -> bool:
        """更新用户信息"""
        if not update_data:
            return False

        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # 构建更新语句
            set_clauses = []
            values = []
            for key, value in update_data.items():
                set_clauses.append(f"{key} = ?")
                values.append(value)

            values.append(user_id)

            query = f"UPDATE users SET {', '.join(set_clauses)} WHERE user_id = ?"
            cursor.execute(query, values)
            conn.commit()

            return cursor.rowcount > 0

    def update_user_last_active(self, user_id: str):
        """更新用户最后活跃时间"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute(
                'UPDATE users SET last_active = CURRENT_TIMESTAMP WHERE user_id = ?',
                (user_id,)
            )
            conn.commit()

    def get_users_list(self, offset: int = 0, limit: int = 20, status: str = None) -> Tuple[List[Dict], int]:
        """获取用户列表"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # 构建查询条件
            where_clause = ""
            params = []
            if status:
                where_clause = "WHERE status = ?"
                params.append(status)

            # 获取总数
            count_query = f"SELECT COUNT(*) FROM users {where_clause}"
            cursor.execute(count_query, params)
            total = cursor.fetchone()[0]

            # 获取用户列表
            list_query = f"""
                SELECT user_id, username, nickname, email, status, created_at, last_active
                FROM users {where_clause}
                ORDER BY created_at DESC
                LIMIT ? OFFSET ?
            """
            cursor.execute(list_query, params + [limit, offset])
            users = cursor.fetchall()

            user_list = [
                {
                    'user_id': user[0],
                    'username': user[1],
                    'nickname': user[2],
                    'email': user[3],
                    'status': user[4],
                    'created_at': user[5],
                    'last_active': user[6]
                }
                for user in users
            ]

            return user_list, total

    # 会话管理方法
    def create_session(self, session_data: Dict):
        """创建用户会话"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO user_sessions (session_token, user_id, created_at, expires_at, is_active)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                session_data['session_token'],
                session_data['user_id'],
                session_data['created_at'],
                session_data['expires_at'],
                session_data.get('is_active', True)
            ))
            conn.commit()

    def get_session(self, session_token: str) -> Optional[Dict]:
        """获取会话信息"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM user_sessions WHERE session_token = ?', (session_token,))
            session = cursor.fetchone()

            if session:
                return {
                    'id': session[0],
                    'session_token': session[1],
                    'user_id': session[2],
                    'created_at': session[3],
                    'expires_at': session[4],
                    'is_active': bool(session[5])
                }
            return None

    def deactivate_session(self, session_token: str) -> bool:
        """停用会话"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute(
                'UPDATE user_sessions SET is_active = 0 WHERE session_token = ?',
                (session_token,)
            )
            conn.commit()
            return cursor.rowcount > 0

    def cleanup_expired_sessions(self):
        """清理过期会话"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute(
                'UPDATE user_sessions SET is_active = 0 WHERE expires_at < CURRENT_TIMESTAMP'
            )
            conn.commit()
            return cursor.rowcount
