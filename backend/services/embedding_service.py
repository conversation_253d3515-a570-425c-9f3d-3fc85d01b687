"""
嵌入服务模块
支持火山引擎 doubao-embedding 和 sentence-transformers 两种嵌入方式
"""

import logging
import numpy as np
from typing import List, Union
from config import Config

logger = logging.getLogger(__name__)


class EmbeddingService:
    """统一的嵌入服务类"""

    def __init__(self):
        """初始化嵌入服务"""
        self.config = Config.VECTOR_DB_CONFIG
        self.use_volcengine = self.config.get('use_volcengine_embedding', True)
        self.model_name = self.config.get('embedding_model', 'doubao-embedding-text-240715')

        if self.use_volcengine:
            self._init_volcengine_embedding()
        else:
            self._init_sentence_transformer()

    def _init_volcengine_embedding(self):
        """初始化火山引擎嵌入服务"""
        try:
            from volcenginesdkarkruntime import Ark

            self.client = Ark(
                base_url="https://ark.cn-beijing.volces.com/api/v3",
            )

            # 测试连接
            test_response = self.client.embeddings.create(
                model=self.model_name,
                input=["测试连接"]
            )

            if test_response and hasattr(test_response, 'data') and test_response.data:
                logger.info(f"✅ 火山引擎嵌入服务初始化成功: {self.model_name}")
                self.embedding_dim = len(test_response.data[0].embedding)
                logger.info(f"📏 嵌入维度: {self.embedding_dim}")
            else:
                raise Exception("测试响应格式不正确")

        except ImportError:
            logger.warning("⚠️  volcenginesdkarkruntime 未安装")
            logger.info("🔄 降级到 sentence-transformers")
            self.use_volcengine = False
            self._init_sentence_transformer()
        except Exception as e:
            logger.warning(f"⚠️  火山引擎嵌入服务初始化失败: {e}")
            logger.info("🔄 降级到 sentence-transformers")
            self.use_volcengine = False
            self._init_sentence_transformer()

    def _init_sentence_transformer(self):
        """初始化 sentence-transformers 嵌入服务"""
        try:
            from sentence_transformers import SentenceTransformer

            # 使用备用模型
            fallback_model = 'all-MiniLM-L6-v2'
            self.model = SentenceTransformer(fallback_model)

            # 测试嵌入
            test_embedding = self.model.encode(["测试连接"])
            self.embedding_dim = len(test_embedding[0])

            logger.info(f"✅ Sentence-Transformers 嵌入服务初始化成功: {fallback_model}")
            logger.info(f"📏 嵌入维度: {self.embedding_dim}")

        except ImportError:
            logger.error("❌ sentence-transformers 未安装，请运行: pip install sentence-transformers")
            raise
        except Exception as e:
            logger.error(f"❌ Sentence-Transformers 嵌入服务初始化失败: {e}")
            raise

    def encode(self, texts: Union[str, List[str]]) -> Union[List[float], List[List[float]]]:
        """
        生成文本嵌入

        Args:
            texts: 单个文本或文本列表

        Returns:
            嵌入向量或嵌入向量列表
        """
        # 统一处理输入格式
        if isinstance(texts, str):
            texts = [texts]
            return_single = True
        else:
            return_single = False

        try:
            if self.use_volcengine:
                embeddings = self._encode_with_volcengine(texts)
            else:
                embeddings = self._encode_with_sentence_transformer(texts)

            # 根据输入格式返回结果
            if return_single:
                return embeddings[0]
            else:
                return embeddings

        except Exception as e:
            logger.error(f"💥 文本嵌入生成失败: {e}")
            # 返回零向量作为备用
            if return_single:
                return [0.0] * self.embedding_dim
            else:
                return [[0.0] * self.embedding_dim] * len(texts)

    def _encode_with_volcengine(self, texts: List[str]) -> List[List[float]]:
        """使用火山引擎生成嵌入"""
        try:
            # 批量处理，火山引擎支持批量嵌入
            response = self.client.embeddings.create(
                model=self.model_name,
                input=texts
            )

            if response and hasattr(response, 'data') and response.data:
                embeddings = []
                for item in response.data:
                    embeddings.append(item.embedding)

                logger.debug(f"🔥 火山引擎嵌入生成成功 - 文本数: {len(texts)}, 维度: {len(embeddings[0])}")
                return embeddings
            else:
                raise Exception("火山引擎响应格式不正确")

        except Exception as e:
            logger.error(f"💥 火山引擎嵌入生成失败: {e}")
            raise

    def _encode_with_sentence_transformer(self, texts: List[str]) -> List[List[float]]:
        """使用 sentence-transformers 生成嵌入"""
        try:
            embeddings = self.model.encode(texts)

            # 转换为列表格式
            if isinstance(embeddings, np.ndarray):
                embeddings = embeddings.tolist()

            logger.debug(f"🤖 Sentence-Transformers 嵌入生成成功 - 文本数: {len(texts)}, 维度: {len(embeddings[0])}")
            return embeddings

        except Exception as e:
            logger.error(f"💥 Sentence-Transformers 嵌入生成失败: {e}")
            raise

    def get_embedding_dim(self) -> int:
        """获取嵌入维度"""
        return self.embedding_dim

    def get_model_info(self) -> dict:
        """获取模型信息"""
        return {
            'provider': 'volcengine' if self.use_volcengine else 'sentence-transformers',
            'model_name': self.model_name if self.use_volcengine else 'all-MiniLM-L6-v2',
            'embedding_dim': self.embedding_dim,
            'is_volcengine': self.use_volcengine
        }


# 全局嵌入服务实例
_embedding_service = None


def get_embedding_service() -> EmbeddingService:
    """获取全局嵌入服务实例"""
    global _embedding_service
    if _embedding_service is None:
        _embedding_service = EmbeddingService()
    return _embedding_service


def create_embeddings(texts: Union[str, List[str]]) -> Union[List[float], List[List[float]]]:
    """
    便捷函数：生成文本嵌入

    Args:
        texts: 单个文本或文本列表

    Returns:
        嵌入向量或嵌入向量列表
    """
    service = get_embedding_service()
    return service.encode(texts)
