"""
记忆管理模块
负责处理用户记忆的提取、存储、检索和管理
"""

import json
import logging
import re
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from models.database import DatabaseManager
from services.prompt_manager import PromptManager

# 配置日志
logger = logging.getLogger(__name__)

class MemoryManager:
    """记忆管理器"""

    def __init__(self, llm_service=None, db_manager: DatabaseManager = None):
        """
        初始化记忆管理器

        Args:
            llm_service: LLM服务实例，用于智能记忆提取
            db_manager: 数据库管理器实例
        """
        self.llm_service = llm_service
        self.db = db_manager or DatabaseManager()
        self.prompt_manager = PromptManager()

        # 记忆类型定义
        self.memory_types = {
            'personal': '个人信息',
            'preference': '喜好偏好',
            'experience': '经历体验',
            'goal': '目标计划',
            'relationship': '关系状态',
            'emotion': '情感状态'
        }

        # 重要性等级
        self.importance_levels = {
            'low': 1.0,
            'medium': 2.0,
            'high': 3.0
        }

    def extract_memories_from_text(self, user_id: str, text: str) -> List[Dict]:
        """
        从文本中提取记忆信息

        Args:
            user_id: 用户ID
            text: 输入文本

        Returns:
            提取的记忆信息列表
        """
        request_id = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        logger.info(f"🧠 开始记忆提取 [{request_id}] - 用户: {user_id}")
        logger.info(f"📝 提取文本: {text}")

        memories = []

        # 如果有LLM服务，使用智能提取
        if self.llm_service:
            try:
                memories = self._extract_memories_with_llm(text, request_id)
            except Exception as e:
                logger.error(f"💥 LLM记忆提取失败 [{request_id}]: {str(e)}")
                # 降级到规则提取
                memories = self._extract_memories_with_rules(text, request_id)
        else:
            # 使用规则提取
            memories = self._extract_memories_with_rules(text, request_id)

        # 保存提取的记忆
        saved_count = 0
        for memory in memories:
            if self._is_valid_memory(memory):
                self.save_memory(
                    user_id=user_id,
                    memory_type=memory.get('type', 'experience'),
                    content=memory.get('content', ''),
                    importance=float(memory.get('importance', 1.0))
                )
                saved_count += 1

        logger.info(f"✅ 记忆提取完成 [{request_id}] - 提取: {len(memories)} 条, 保存: {saved_count} 条")
        return memories

    def _extract_memories_with_llm(self, text: str, request_id: str) -> List[Dict]:
        """使用LLM进行智能记忆提取"""
        # 使用提示词模块获取记忆提取提示词
        system_prompt = self.prompt_manager.render_template('memory_extraction.j2')

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": f"请从这段话中提取记忆信息：{text}"}
        ]

        # 调用LLM服务
        if hasattr(self.llm_service, 'client') and hasattr(self.llm_service, 'model'):
            # 真实的LLM服务
            result = self.llm_service.client.chat_completions(
                messages=messages,
                model=self.llm_service.model,
                temperature=0.3,
                max_tokens=300
            )
        else:
            # 模拟LLM服务，直接返回空结果
            result = {"success": False, "error": "模拟LLM服务不支持记忆提取"}

        if result["success"]:
            content = result["content"]
            logger.info(f"📄 LLM原始响应: {content}")

            try:
                memories = json.loads(content)
                if isinstance(memories, list):
                    logger.info(f"✅ LLM记忆提取成功 [{request_id}] - {len(memories)} 条")
                    for i, memory in enumerate(memories):
                        logger.info(f"   {i+1}. [{memory.get('type', 'unknown')}] {memory.get('content', '')}")
                    return memories
                else:
                    logger.warning(f"⚠️  返回格式不是列表，使用空列表")
                    return []
            except json.JSONDecodeError:
                logger.warning(f"⚠️  JSON解析失败，尝试清理内容")
                try:
                    # 移除可能的markdown代码块格式
                    cleaned_content = re.sub(r'```(?:json)?\s*([\s\S]*?)\s*```', r'\1', content)
                    memories = json.loads(cleaned_content)
                    if isinstance(memories, list):
                        logger.info(f"✅ 清理后解析成功")
                        return memories
                    else:
                        return []
                except:
                    logger.warning(f"⚠️  清理后仍解析失败，使用空列表")
                    return []
        else:
            logger.error(f"❌ LLM记忆提取API错误: {result.get('error', '未知错误')}")
            return []

    def _extract_memories_with_rules(self, text: str, request_id: str) -> List[Dict]:
        """使用规则进行记忆提取"""
        logger.info(f"🔧 使用规则提取记忆 [{request_id}]")

        memories = []
        text_lower = text.lower()

        # 个人信息提取
        if any(word in text_lower for word in ['我叫', '我是', '我的名字', '我来自', '我住在']):
            memories.append({
                "type": "personal",
                "content": f"用户提到了个人身份信息: {text[:50]}...",
                "importance": "2.5"
            })

        # 喜好偏好提取
        if any(word in text_lower for word in ['喜欢', '爱好', '兴趣', '偏爱', '热爱', '讨厌', '不喜欢']):
            memories.append({
                "type": "preference",
                "content": f"用户分享了喜好偏好: {text[:50]}...",
                "importance": "2.0"
            })

        # 经历体验提取
        if any(word in text_lower for word in ['昨天', '今天', '最近', '之前', '经历', '去了', '做了', '参加了']):
            memories.append({
                "type": "experience",
                "content": f"用户分享了个人经历: {text[:50]}...",
                "importance": "1.5"
            })

        # 目标计划提取
        if any(word in text_lower for word in ['想要', '计划', '目标', '希望', '打算', '准备']):
            memories.append({
                "type": "goal",
                "content": f"用户提到了目标计划: {text[:50]}...",
                "importance": "2.0"
            })

        # 情感状态提取
        if any(word in text_lower for word in ['开心', '难过', '生气', '焦虑', '兴奋', '失望', '满意']):
            memories.append({
                "type": "emotion",
                "content": f"用户表达了情感状态: {text[:50]}...",
                "importance": "1.5"
            })

        logger.info(f"🔧 规则提取完成 [{request_id}] - {len(memories)} 条")
        return memories

    def _is_valid_memory(self, memory: Dict) -> bool:
        """验证记忆是否有效"""
        if not isinstance(memory, dict):
            return False

        # 检查必要字段
        if not memory.get('content') or not memory.get('type'):
            return False

        # 检查记忆类型是否有效
        if memory.get('type') not in self.memory_types:
            return False

        # 检查重要性是否在合理范围内
        try:
            importance = float(memory.get('importance', 1.0))
            if importance < 0.5 or importance > 3.0:
                return False
        except (ValueError, TypeError):
            return False

        return True

    def save_memory(self, user_id: str, memory_type: str, content: str, importance: float = 1.0):
        """
        保存记忆到数据库

        Args:
            user_id: 用户ID
            memory_type: 记忆类型
            content: 记忆内容
            importance: 重要程度
        """
        try:
            self.db.save_memory(user_id, memory_type, content, importance)
            logger.info(f"💾 记忆已保存 - 用户: {user_id}, 类型: {memory_type}, 重要性: {importance}")
        except Exception as e:
            logger.error(f"💥 记忆保存失败 - 用户: {user_id}, 错误: {str(e)}")

    def get_relevant_memories(self, user_id: str, keywords: List[str] = None, limit: int = 5) -> List[Dict]:
        """
        获取相关记忆

        Args:
            user_id: 用户ID
            keywords: 关键词列表
            limit: 返回数量限制

        Returns:
            相关记忆列表
        """
        try:
            memories = self.db.get_relevant_memories(user_id, keywords, limit)
            logger.info(f"🔍 获取相关记忆 - 用户: {user_id}, 关键词: {keywords}, 结果: {len(memories)} 条")
            return memories
        except Exception as e:
            logger.error(f"💥 获取记忆失败 - 用户: {user_id}, 错误: {str(e)}")
            return []

    def extract_keywords(self, text: str) -> List[str]:
        """
        从文本中提取关键词用于记忆检索

        Args:
            text: 输入文本

        Returns:
            关键词列表
        """
        keywords = []

        # 常见的重要词汇
        important_words = [
            '工作', '学习', '家人', '朋友', '爱好', '电影', '音乐', '书', '旅行',
            '梦想', '目标', '困难', '开心', '难过', '压力', '放松', '运动', '美食',
            '学校', '公司', '家', '咖啡', '画画', '艺术', '设计', '创作', '灵感'
        ]

        for word in important_words:
            if word in text:
                keywords.append(word)

        # 使用简单的分词提取更多关键词
        # 这里可以后续集成更高级的NLP库
        words = re.findall(r'[\u4e00-\u9fff]+', text)  # 提取中文词汇
        for word in words:
            if len(word) >= 2 and word not in keywords:
                keywords.append(word)

        return keywords[:8]  # 最多返回8个关键词

    def get_memory_summary(self, user_id: str) -> Dict:
        """
        获取用户记忆摘要

        Args:
            user_id: 用户ID

        Returns:
            记忆摘要信息
        """
        try:
            all_memories = self.db.get_relevant_memories(user_id, limit=100)

            # 按类型统计
            type_counts = {}
            total_importance = 0

            for memory in all_memories:
                mem_type = memory.get('type', 'unknown')
                type_counts[mem_type] = type_counts.get(mem_type, 0) + 1
                total_importance += memory.get('importance', 1.0)

            return {
                'total_memories': len(all_memories),
                'type_distribution': type_counts,
                'average_importance': total_importance / len(all_memories) if all_memories else 0,
                'most_common_type': max(type_counts.items(), key=lambda x: x[1])[0] if type_counts else None
            }
        except Exception as e:
            logger.error(f"💥 获取记忆摘要失败 - 用户: {user_id}, 错误: {str(e)}")
            return {
                'total_memories': 0,
                'type_distribution': {},
                'average_importance': 0,
                'most_common_type': None
            }
