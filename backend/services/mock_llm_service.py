"""
模拟LLM服务 - 用于测试和演示
当火山引擎API不可用时，使用预设回复
"""

import logging
from datetime import datetime
from typing import Dict, List
from clients.chat_completions import create_client

# 配置日志
logger = logging.getLogger(__name__)

class MockLLMService:
    def __init__(self):
        # 创建模拟客户端
        self.client = create_client(provider="mock")

    def generate_response(
        self,
        user_message: str,
        system_prompt: str,
        conversation_history: List[Dict] = None,
        temperature: float = 0.7
    ) -> str:
        """
        生成模拟回复
        """
        request_id = datetime.now().strftime("%Y%m%d_%H%M%S_%f")

        # 记录请求日志
        logger.info(f"🤖 模拟LLM请求 [{request_id}] - generate_response()")
        logger.info(f"📝 用户消息: {user_message}")
        logger.info(f"🎭 系统提示词: {system_prompt[:200]}...")
        logger.info(f"📚 对话历史: {len(conversation_history) if conversation_history else 0} 条")
        logger.info(f"⚙️  请求参数: temperature={temperature}")

        # 构建消息列表
        messages = [{"role": "system", "content": system_prompt}]

        # 添加对话历史
        if conversation_history:
            for conv in conversation_history[-10:]:  # 只保留最近10轮对话
                role = "user" if conv['sender'] == 'user' else "assistant"
                messages.append({"role": role, "content": conv['message']})

        # 添加当前用户消息
        messages.append({"role": "user", "content": user_message})

        # 调用模拟客户端
        result = self.client.chat_completions(
            messages=messages,
            model="mock-model",
            temperature=temperature,
            max_tokens=1000
        )

        if result["success"]:
            response = result["content"]

            # 记录响应日志
            logger.info(f"✅ 模拟LLM响应成功 [{request_id}] - generate_response()")
            logger.info(f"🎯 选择回复类型: {result.get('response_type', 'unknown')}")
            logger.info(f"💬 生成回复: {response}")
            logger.info(f"📊 响应统计: {len(response)} 字符")

            return response
        else:
            logger.error(f"❌ 模拟LLM错误 [{request_id}]: {result.get('error', '未知错误')}")
            return "抱歉，我现在有点累了，能稍后再聊吗？"

    def analyze_emotion(self, text: str) -> Dict[str, float]:
        """
        模拟情感分析
        """
        request_id = datetime.now().strftime("%Y%m%d_%H%M%S_%f")

        logger.info(f"🎭 模拟情感分析请求 [{request_id}] - analyze_emotion()")
        logger.info(f"📝 分析文本: {text}")

        text_lower = text.lower()

        # 简单的关键词匹配
        if any(word in text_lower for word in ['开心', '高兴', '快乐', '棒', '好', '喜欢']):
            result = {
                "emotion": "happy",
                "intensity": 0.8,
                "keywords": ["开心", "快乐"]
            }
        elif any(word in text_lower for word in ['难过', '伤心', '沮丧', '不开心', '郁闷']):
            result = {
                "emotion": "sad",
                "intensity": 0.7,
                "keywords": ["难过", "伤心"]
            }
        elif any(word in text_lower for word in ['生气', '愤怒', '气愤', '讨厌']):
            result = {
                "emotion": "angry",
                "intensity": 0.6,
                "keywords": ["生气", "愤怒"]
            }
        elif any(word in text_lower for word in ['紧张', '焦虑', '担心', '害怕']):
            result = {
                "emotion": "anxious",
                "intensity": 0.7,
                "keywords": ["紧张", "焦虑"]
            }
        elif any(word in text_lower for word in ['兴奋', '激动', '期待']):
            result = {
                "emotion": "excited",
                "intensity": 0.8,
                "keywords": ["兴奋", "激动"]
            }
        else:
            result = {
                "emotion": "neutral",
                "intensity": 0.5,
                "keywords": []
            }

        logger.info(f"✅ 模拟情感分析成功 [{request_id}] - analyze_emotion()")
        logger.info(f"😊 情感结果: {result}")

        return result


