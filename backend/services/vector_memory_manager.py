"""
向量记忆管理模块
使用Chroma向量数据库进行语义记忆存储和检索
"""

import json
import logging
import re
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from sentence_transformers import SentenceTransformer
from services.prompt_manager import PromptManager
from config import Config

# 配置日志
logger = logging.getLogger(__name__)

class VectorMemoryManager:
    """基于向量数据库的记忆管理器"""

    def __init__(self, llm_service=None, persist_directory: str = None):
        """
        初始化向量记忆管理器

        Args:
            llm_service: LLM服务实例，用于智能记忆提取
            persist_directory: Chroma数据库持久化目录
        """
        self.llm_service = llm_service
        self.prompt_manager = PromptManager()

        # 使用配置中的设置
        self.config = Config.VECTOR_DB_CONFIG
        self.persist_directory = persist_directory or self.config['persist_directory']

        # 初始化嵌入模型
        try:
            embedding_model_name = self.config['embedding_model']
            self.embedding_model = SentenceTransformer(embedding_model_name)
            logger.info(f"✅ 嵌入模型加载成功: {embedding_model_name}")
        except Exception as e:
            logger.error(f"❌ 嵌入模型加载失败: {e}")
            raise

        # 初始化Chroma客户端
        self._init_chroma_client()

        # 记忆类型定义
        self.memory_types = {
            'personal': '个人信息',
            'preference': '喜好偏好',
            'experience': '经历体验',
            'goal': '目标计划',
            'relationship': '关系状态',
            'emotion': '情感状态'
        }

        # 重要性等级
        self.importance_levels = {
            'low': 1.0,
            'medium': 2.0,
            'high': 3.0
        }

    def _init_chroma_client(self):
        """初始化Chroma客户端和集合"""
        try:
            import chromadb
            from chromadb.config import Settings

            # 创建持久化客户端
            self.chroma_client = chromadb.PersistentClient(
                path=self.persist_directory,
                settings=Settings(
                    anonymized_telemetry=False,
                    allow_reset=True
                )
            )

            # 创建或获取记忆集合
            self.memory_collection = self.chroma_client.get_or_create_collection(
                name="user_memories",
                metadata={"description": "用户记忆向量存储"}
            )

            logger.info(f"✅ Chroma向量数据库初始化成功，持久化目录: {self.persist_directory}")

        except ImportError:
            logger.error("❌ ChromaDB未安装，请运行: pip install chromadb")
            raise
        except Exception as e:
            logger.error(f"❌ Chroma客户端初始化失败: {e}")
            raise

    def extract_memories_from_text(self, user_id: str, text: str) -> List[Dict]:
        """
        从文本中提取记忆信息

        Args:
            user_id: 用户ID
            text: 输入文本

        Returns:
            提取的记忆信息列表
        """
        request_id = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        logger.info(f"🧠 开始向量记忆提取 [{request_id}] - 用户: {user_id}")
        logger.info(f"📝 提取文本: {text}")

        memories = []

        # 如果有LLM服务，使用智能提取
        if self.llm_service:
            try:
                memories = self._extract_memories_with_llm(text, request_id)
            except Exception as e:
                logger.error(f"💥 LLM记忆提取失败 [{request_id}]: {str(e)}")
                # 降级到规则提取
                memories = self._extract_memories_with_rules(text, request_id)
        else:
            # 使用规则提取
            memories = self._extract_memories_with_rules(text, request_id)

        # 保存提取的记忆到向量数据库
        saved_count = 0
        for memory in memories:
            if self._is_valid_memory(memory):
                self.save_memory(
                    user_id=user_id,
                    memory_type=memory.get('type', 'experience'),
                    content=memory.get('content', ''),
                    importance=float(memory.get('importance', 1.0))
                )
                saved_count += 1

        logger.info(f"✅ 向量记忆提取完成 [{request_id}] - 提取: {len(memories)} 条, 保存: {saved_count} 条")
        return memories

    def _extract_memories_with_llm(self, text: str, request_id: str) -> List[Dict]:
        """使用LLM进行智能记忆提取"""
        # 使用提示词模块获取记忆提取提示词
        system_prompt = self.prompt_manager.render_template('memory_extraction.j2')

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": f"请从这段话中提取记忆信息：{text}"}
        ]

        # 调用LLM服务
        if hasattr(self.llm_service, 'client') and hasattr(self.llm_service, 'model'):
            # 真实的LLM服务
            result = self.llm_service.client.chat_completions(
                messages=messages,
                model=self.llm_service.model,
                temperature=0.3,
                max_tokens=300
            )
        else:
            # 模拟LLM服务，直接返回空结果
            result = {"success": False, "error": "模拟LLM服务不支持记忆提取"}

        if result["success"]:
            content = result["content"]
            logger.info(f"📄 LLM原始响应: {content}")

            try:
                memories = json.loads(content)
                if isinstance(memories, list):
                    logger.info(f"✅ LLM记忆提取成功 [{request_id}] - {len(memories)} 条")
                    for i, memory in enumerate(memories):
                        logger.info(f"   {i+1}. [{memory.get('type', 'unknown')}] {memory.get('content', '')}")
                    return memories
                else:
                    logger.warning(f"⚠️  返回格式不是列表，使用空列表")
                    return []
            except json.JSONDecodeError:
                logger.warning(f"⚠️  JSON解析失败，尝试清理内容")
                try:
                    # 移除可能的markdown代码块格式
                    cleaned_content = re.sub(r'```(?:json)?\s*([\s\S]*?)\s*```', r'\1', content)
                    memories = json.loads(cleaned_content)
                    if isinstance(memories, list):
                        logger.info(f"✅ 清理后解析成功")
                        return memories
                    else:
                        return []
                except:
                    logger.warning(f"⚠️  清理后仍解析失败，使用空列表")
                    return []
        else:
            logger.error(f"❌ LLM记忆提取API错误: {result.get('error', '未知错误')}")
            return []

    def _extract_memories_with_rules(self, text: str, request_id: str) -> List[Dict]:
        """使用规则进行记忆提取"""
        logger.info(f"🔧 使用规则提取记忆 [{request_id}]")

        memories = []
        text_lower = text.lower()

        # 个人信息提取
        if any(word in text_lower for word in ['我叫', '我是', '我的名字', '我来自', '我住在']):
            memories.append({
                "type": "personal",
                "content": f"用户提到了个人身份信息: {text[:50]}...",
                "importance": "2.5"
            })

        # 喜好偏好提取
        if any(word in text_lower for word in ['喜欢', '爱好', '兴趣', '偏爱', '热爱', '讨厌', '不喜欢']):
            memories.append({
                "type": "preference",
                "content": f"用户分享了喜好偏好: {text[:50]}...",
                "importance": "2.0"
            })

        # 经历体验提取
        if any(word in text_lower for word in ['昨天', '今天', '最近', '之前', '经历', '去了', '做了', '参加了']):
            memories.append({
                "type": "experience",
                "content": f"用户分享了个人经历: {text[:50]}...",
                "importance": "1.5"
            })

        # 目标计划提取
        if any(word in text_lower for word in ['想要', '计划', '目标', '希望', '打算', '准备']):
            memories.append({
                "type": "goal",
                "content": f"用户提到了目标计划: {text[:50]}...",
                "importance": "2.0"
            })

        # 情感状态提取
        if any(word in text_lower for word in ['开心', '难过', '生气', '焦虑', '兴奋', '失望', '满意']):
            memories.append({
                "type": "emotion",
                "content": f"用户表达了情感状态: {text[:50]}...",
                "importance": "1.5"
            })

        logger.info(f"🔧 规则提取完成 [{request_id}] - {len(memories)} 条")
        return memories

    def _is_valid_memory(self, memory: Dict) -> bool:
        """验证记忆是否有效"""
        if not isinstance(memory, dict):
            return False

        # 检查必要字段
        if not memory.get('content') or not memory.get('type'):
            return False

        # 检查记忆类型是否有效
        if memory.get('type') not in self.memory_types:
            return False

        # 检查重要性是否在合理范围内
        try:
            importance = float(memory.get('importance', 1.0))
            if importance < 0.5 or importance > 3.0:
                return False
        except (ValueError, TypeError):
            return False

        return True

    def save_memory(self, user_id: str, memory_type: str, content: str, importance: float = 1.0):
        """
        保存记忆到向量数据库

        Args:
            user_id: 用户ID
            memory_type: 记忆类型
            content: 记忆内容
            importance: 重要程度
        """
        try:
            # 生成唯一ID
            memory_id = str(uuid.uuid4())

            # 生成文本嵌入
            embedding = self.embedding_model.encode([content])[0].tolist()

            # 准备元数据
            metadata = {
                "user_id": user_id,
                "memory_type": memory_type,
                "importance": importance,
                "created_at": datetime.now().isoformat(),
                "recall_count": 0
            }

            # 添加到向量数据库
            self.memory_collection.add(
                ids=[memory_id],
                embeddings=[embedding],
                documents=[content],
                metadatas=[metadata]
            )

            logger.info(f"💾 向量记忆已保存 - 用户: {user_id}, 类型: {memory_type}, 重要性: {importance}")

        except Exception as e:
            logger.error(f"💥 向量记忆保存失败 - 用户: {user_id}, 错误: {str(e)}")

    def get_relevant_memories(self, user_id: str, query_text: str = None, keywords: List[str] = None,
                            limit: int = None, similarity_threshold: float = None) -> List[Dict]:
        """
        获取相关记忆（基于语义相似性）

        Args:
            user_id: 用户ID
            query_text: 查询文本（用于语义搜索）
            keywords: 关键词列表（备用）
            limit: 返回数量限制
            similarity_threshold: 相似度阈值

        Returns:
            相关记忆列表
        """
        # 使用配置中的默认值
        if limit is None:
            limit = self.config['max_memories_per_query']
        if similarity_threshold is None:
            similarity_threshold = self.config['similarity_threshold']

        try:
            if query_text:
                # 使用语义搜索
                query_embedding = self.embedding_model.encode([query_text])[0].tolist()

                # 查询向量数据库
                results = self.memory_collection.query(
                    query_embeddings=[query_embedding],
                    n_results=limit * 2,  # 获取更多结果以便过滤
                    where={"user_id": user_id}
                )

                memories = []
                if results['documents'] and results['documents'][0]:
                    for i, (doc, metadata, distance) in enumerate(zip(
                        results['documents'][0],
                        results['metadatas'][0],
                        results['distances'][0]
                    )):
                        # 计算相似度分数（距离越小，相似度越高）
                        similarity = 1 - distance

                        if similarity >= similarity_threshold:
                            # 更新召回次数
                            self._update_recall_count(results['ids'][0][i])

                            memories.append({
                                'content': doc,
                                'type': metadata.get('memory_type', 'unknown'),
                                'importance': metadata.get('importance', 1.0),
                                'created_at': metadata.get('created_at'),
                                'similarity': similarity,
                                'recall_count': metadata.get('recall_count', 0) + 1
                            })

                # 按重要性和相似度排序
                memories.sort(key=lambda x: (x['importance'] * x['similarity']), reverse=True)
                memories = memories[:limit]

                logger.info(f"🔍 语义搜索获取记忆 - 用户: {user_id}, 查询: {query_text[:30]}..., 结果: {len(memories)} 条")

            else:
                # 降级到关键词搜索或获取最近记忆
                memories = self._get_memories_by_keywords(user_id, keywords, limit)

            return memories

        except Exception as e:
            logger.error(f"💥 获取向量记忆失败 - 用户: {user_id}, 错误: {str(e)}")
            return []

    def _get_memories_by_keywords(self, user_id: str, keywords: List[str] = None, limit: int = 5) -> List[Dict]:
        """基于关键词获取记忆（备用方法）"""
        try:
            # 获取用户的所有记忆
            results = self.memory_collection.get(
                where={"user_id": user_id},
                limit=limit * 3  # 获取更多以便过滤
            )

            memories = []
            if results['documents']:
                for i, (doc, metadata) in enumerate(zip(results['documents'], results['metadatas'])):
                    # 如果有关键词，进行简单匹配
                    if keywords:
                        doc_lower = doc.lower()
                        if any(keyword.lower() in doc_lower for keyword in keywords):
                            memories.append({
                                'content': doc,
                                'type': metadata.get('memory_type', 'unknown'),
                                'importance': metadata.get('importance', 1.0),
                                'created_at': metadata.get('created_at'),
                                'recall_count': metadata.get('recall_count', 0)
                            })
                    else:
                        # 没有关键词时返回最重要的记忆
                        memories.append({
                            'content': doc,
                            'type': metadata.get('memory_type', 'unknown'),
                            'importance': metadata.get('importance', 1.0),
                            'created_at': metadata.get('created_at'),
                            'recall_count': metadata.get('recall_count', 0)
                        })

            # 按重要性排序
            memories.sort(key=lambda x: x['importance'], reverse=True)
            return memories[:limit]

        except Exception as e:
            logger.error(f"💥 关键词搜索记忆失败 - 用户: {user_id}, 错误: {str(e)}")
            return []

    def _update_recall_count(self, memory_id: str):
        """更新记忆的召回次数"""
        try:
            # 获取当前记忆
            result = self.memory_collection.get(ids=[memory_id])
            if result['metadatas'] and result['metadatas'][0]:
                metadata = result['metadatas'][0]
                metadata['recall_count'] = metadata.get('recall_count', 0) + 1
                metadata['last_recalled'] = datetime.now().isoformat()

                # 更新元数据
                self.memory_collection.update(
                    ids=[memory_id],
                    metadatas=[metadata]
                )

        except Exception as e:
            logger.error(f"💥 更新召回次数失败 - 记忆ID: {memory_id}, 错误: {str(e)}")

    def extract_keywords(self, text: str) -> List[str]:
        """
        从文本中提取关键词用于记忆检索

        Args:
            text: 输入文本

        Returns:
            关键词列表
        """
        keywords = []

        # 常见的重要词汇
        important_words = [
            '工作', '学习', '家人', '朋友', '爱好', '电影', '音乐', '书', '旅行',
            '梦想', '目标', '困难', '开心', '难过', '压力', '放松', '运动', '美食',
            '学校', '公司', '家', '咖啡', '画画', '艺术', '设计', '创作', '灵感'
        ]

        for word in important_words:
            if word in text:
                keywords.append(word)

        # 使用简单的分词提取更多关键词
        words = re.findall(r'[\u4e00-\u9fff]+', text)  # 提取中文词汇
        for word in words:
            if len(word) >= 2 and word not in keywords:
                keywords.append(word)

        return keywords[:8]  # 最多返回8个关键词

    def get_memory_summary(self, user_id: str) -> Dict:
        """
        获取用户记忆摘要

        Args:
            user_id: 用户ID

        Returns:
            记忆摘要信息
        """
        try:
            # 获取用户的所有记忆
            results = self.memory_collection.get(
                where={"user_id": user_id},
                limit=1000  # 获取大量记忆用于统计
            )

            if not results['documents']:
                return {
                    'total_memories': 0,
                    'type_distribution': {},
                    'average_importance': 0,
                    'most_common_type': None,
                    'total_recalls': 0
                }

            # 按类型统计
            type_counts = {}
            total_importance = 0
            total_recalls = 0

            for metadata in results['metadatas']:
                mem_type = metadata.get('memory_type', 'unknown')
                type_counts[mem_type] = type_counts.get(mem_type, 0) + 1
                total_importance += metadata.get('importance', 1.0)
                total_recalls += metadata.get('recall_count', 0)

            return {
                'total_memories': len(results['documents']),
                'type_distribution': type_counts,
                'average_importance': total_importance / len(results['documents']),
                'most_common_type': max(type_counts.items(), key=lambda x: x[1])[0] if type_counts else None,
                'total_recalls': total_recalls
            }

        except Exception as e:
            logger.error(f"💥 获取向量记忆摘要失败 - 用户: {user_id}, 错误: {str(e)}")
            return {
                'total_memories': 0,
                'type_distribution': {},
                'average_importance': 0,
                'most_common_type': None,
                'total_recalls': 0
            }

    def delete_memory(self, memory_id: str) -> bool:
        """
        删除指定记忆

        Args:
            memory_id: 记忆ID

        Returns:
            是否删除成功
        """
        try:
            self.memory_collection.delete(ids=[memory_id])
            logger.info(f"🗑️ 记忆已删除 - ID: {memory_id}")
            return True
        except Exception as e:
            logger.error(f"💥 删除记忆失败 - ID: {memory_id}, 错误: {str(e)}")
            return False

    def clear_user_memories(self, user_id: str) -> bool:
        """
        清除用户的所有记忆

        Args:
            user_id: 用户ID

        Returns:
            是否清除成功
        """
        try:
            # 获取用户的所有记忆ID
            results = self.memory_collection.get(
                where={"user_id": user_id}
            )

            if results['ids']:
                self.memory_collection.delete(ids=results['ids'])
                logger.info(f"🗑️ 用户记忆已清除 - 用户: {user_id}, 数量: {len(results['ids'])}")
                return True
            else:
                logger.info(f"ℹ️ 用户无记忆需要清除 - 用户: {user_id}")
                return True

        except Exception as e:
            logger.error(f"💥 清除用户记忆失败 - 用户: {user_id}, 错误: {str(e)}")
            return False

    def search_memories(self, user_id: str, search_query: str, limit: int = 10) -> List[Dict]:
        """
        搜索用户记忆

        Args:
            user_id: 用户ID
            search_query: 搜索查询
            limit: 返回数量限制

        Returns:
            搜索结果列表
        """
        return self.get_relevant_memories(
            user_id=user_id,
            query_text=search_query,
            limit=limit,
            similarity_threshold=0.5  # 降低阈值以获取更多结果
        )

    def get_memory_statistics(self) -> Dict:
        """
        获取整体记忆统计信息

        Returns:
            统计信息字典
        """
        try:
            # 获取集合信息
            collection_count = self.memory_collection.count()

            # 获取所有记忆的元数据进行统计
            results = self.memory_collection.get(limit=collection_count)

            if not results['metadatas']:
                return {
                    'total_memories': 0,
                    'total_users': 0,
                    'memory_types': {},
                    'average_importance': 0
                }

            # 统计用户数量
            users = set()
            memory_types = {}
            total_importance = 0

            for metadata in results['metadatas']:
                users.add(metadata.get('user_id'))
                mem_type = metadata.get('memory_type', 'unknown')
                memory_types[mem_type] = memory_types.get(mem_type, 0) + 1
                total_importance += metadata.get('importance', 1.0)

            return {
                'total_memories': collection_count,
                'total_users': len(users),
                'memory_types': memory_types,
                'average_importance': total_importance / collection_count if collection_count > 0 else 0
            }

        except Exception as e:
            logger.error(f"💥 获取记忆统计失败: {str(e)}")
            return {
                'total_memories': 0,
                'total_users': 0,
                'memory_types': {},
                'average_importance': 0
            }
