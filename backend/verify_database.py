#!/usr/bin/env python3
"""
验证数据库结构脚本
"""

import sqlite3
from config import Config

def verify_database_structure():
    """验证数据库结构"""
    db_path = Config.DATABASE_PATH
    
    print("🔍 验证数据库结构...")
    print(f"📁 数据库路径: {db_path}")
    print("=" * 60)
    
    with sqlite3.connect(db_path) as conn:
        cursor = conn.cursor()
        
        # 获取所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print("📋 数据库表列表:")
        for table in tables:
            print(f"  - {table[0]}")
        
        print("\n" + "=" * 60)
        
        # 检查用户表结构
        print("👤 用户表 (users) 结构:")
        cursor.execute("PRAGMA table_info(users)")
        columns = cursor.fetchall()
        
        for col in columns:
            print(f"  {col[1]:15} {col[2]:15} {'NOT NULL' if col[3] else 'NULL':8} {'PK' if col[5] else '':3}")
        
        # 检查用户会话表结构
        print("\n🔐 用户会话表 (user_sessions) 结构:")
        cursor.execute("PRAGMA table_info(user_sessions)")
        columns = cursor.fetchall()
        
        for col in columns:
            print(f"  {col[1]:15} {col[2]:15} {'NOT NULL' if col[3] else 'NULL':8} {'PK' if col[5] else '':3}")
        
        # 检查索引
        print("\n📊 数据库索引:")
        cursor.execute("SELECT name, tbl_name FROM sqlite_master WHERE type='index' AND name NOT LIKE 'sqlite_%'")
        indexes = cursor.fetchall()
        
        for index in indexes:
            print(f"  {index[0]:25} -> {index[1]}")
        
        # 检查数据
        print("\n📈 数据统计:")
        
        # 用户数量
        cursor.execute("SELECT COUNT(*) FROM users")
        user_count = cursor.fetchone()[0]
        print(f"  用户数量: {user_count}")
        
        # 会话数量
        cursor.execute("SELECT COUNT(*) FROM user_sessions")
        session_count = cursor.fetchone()[0]
        print(f"  会话数量: {session_count}")
        
        # 对话数量
        cursor.execute("SELECT COUNT(*) FROM conversations")
        conversation_count = cursor.fetchone()[0]
        print(f"  对话数量: {conversation_count}")
        
        # 记忆数量
        cursor.execute("SELECT COUNT(*) FROM memories")
        memory_count = cursor.fetchone()[0]
        print(f"  记忆数量: {memory_count}")
        
        print("\n✅ 数据库结构验证完成!")

if __name__ == "__main__":
    verify_database_structure()
