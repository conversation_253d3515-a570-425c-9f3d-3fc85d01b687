<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>记忆管理 - 小雨</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .nav-tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        .nav-tab {
            flex: 1;
            padding: 15px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }

        .nav-tab.active {
            background: white;
            border-bottom-color: #667eea;
            color: #667eea;
            font-weight: bold;
        }

        .nav-tab:hover {
            background: #e9ecef;
        }

        .tab-content {
            padding: 30px;
            min-height: 500px;
        }

        .tab-pane {
            display: none;
        }

        .tab-pane.active {
            display: block;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .stat-card h3 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .stat-card p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .search-box {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .search-input {
            flex: 1;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: #667eea;
        }

        .search-btn, .clear-btn {
            padding: 12px 20px;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .search-btn {
            background: #667eea;
            color: white;
        }

        .search-btn:hover {
            background: #5a6fd8;
        }

        .clear-btn {
            background: #dc3545;
            color: white;
        }

        .clear-btn:hover {
            background: #c82333;
        }

        .memory-list {
            display: grid;
            gap: 15px;
        }

        .memory-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            transition: all 0.3s ease;
        }

        .memory-item:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }

        .memory-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 10px;
        }

        .memory-type {
            background: #667eea;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }

        .memory-importance {
            font-size: 14px;
            color: #6c757d;
        }

        .memory-content {
            font-size: 16px;
            line-height: 1.6;
            color: #333;
            margin-bottom: 10px;
        }

        .memory-meta {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #6c757d;
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #6c757d;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .user-selector {
            margin-bottom: 20px;
        }

        .user-selector select {
            padding: 10px 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 16px;
            min-width: 200px;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }

            .header {
                padding: 20px;
            }

            .header h1 {
                font-size: 2em;
            }

            .tab-content {
                padding: 20px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .search-box {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 记忆管理中心</h1>
            <p>管理小雨的记忆与用户互动记录</p>
        </div>

        <div class="nav-tabs">
            <button class="nav-tab active" onclick="switchTab('overview')">📊 概览</button>
            <button class="nav-tab" onclick="switchTab('user-memories')">👤 用户记忆</button>
            <button class="nav-tab" onclick="switchTab('persona-memories')">💭 个人记忆</button>
        </div>

        <div class="tab-content">
            <!-- 概览标签页 -->
            <div id="overview" class="tab-pane active">
                <div class="stats-grid" id="stats-grid">
                    <div class="loading">正在加载统计信息...</div>
                </div>
            </div>

            <!-- 用户记忆标签页 -->
            <div id="user-memories" class="tab-pane">
                <div class="user-selector">
                    <label for="user-select">选择用户：</label>
                    <select id="user-select" onchange="loadUserMemories()">
                        <option value="">请选择用户</option>
                    </select>
                </div>

                <div class="search-box">
                    <input type="text" class="search-input" id="user-search" placeholder="搜索用户记忆...">
                    <button class="search-btn" onclick="searchUserMemories()">🔍 搜索</button>
                    <button class="clear-btn" onclick="clearUserMemories()">🗑️ 清除记忆</button>
                </div>

                <div id="user-memories-error" class="error" style="display: none;"></div>
                <div id="user-memories-list" class="memory-list">
                    <div class="loading">请选择用户查看记忆</div>
                </div>
            </div>

            <!-- 个人记忆标签页 -->
            <div id="persona-memories" class="tab-pane">
                <div class="search-box">
                    <input type="text" class="search-input" id="persona-search" placeholder="搜索个人记忆...">
                    <button class="search-btn" onclick="searchPersonaMemories()">🔍 搜索</button>
                </div>

                <div id="persona-memories-error" class="error" style="display: none;"></div>
                <div id="persona-memories-list" class="memory-list">
                    <div class="loading">正在加载个人记忆...</div>
                </div>
            </div>
        </div>
    </div>

    <script src="memory-manager.js"></script>
</body>
</html>
