// 记忆管理JavaScript

let currentUser = '';
let users = [];

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadStats();
    loadUsers();
    loadPersonaMemories();
});

// 切换标签页
function switchTab(tabName) {
    // 移除所有活动状态
    document.querySelectorAll('.nav-tab').forEach(tab => tab.classList.remove('active'));
    document.querySelectorAll('.tab-pane').forEach(pane => pane.classList.remove('active'));
    
    // 激活当前标签
    event.target.classList.add('active');
    document.getElementById(tabName).classList.add('active');
    
    // 根据标签页加载相应数据
    if (tabName === 'overview') {
        loadStats();
    } else if (tabName === 'persona-memories') {
        loadPersonaMemories();
    }
}

// 加载统计信息
async function loadStats() {
    try {
        const response = await fetch('/api/memories/stats');
        const data = await response.json();
        
        if (data.success) {
            displayStats(data);
        } else {
            showError('stats-grid', '加载统计信息失败: ' + data.error);
        }
    } catch (error) {
        showError('stats-grid', '网络错误: ' + error.message);
    }
}

// 显示统计信息
function displayStats(data) {
    const statsGrid = document.getElementById('stats-grid');
    const userStats = data.user_memories || {};
    const personaStats = data.persona_memories || {};
    
    statsGrid.innerHTML = `
        <div class="stat-card">
            <h3>${userStats.total_memories || 0}</h3>
            <p>用户记忆总数</p>
        </div>
        <div class="stat-card">
            <h3>${userStats.total_users || 0}</h3>
            <p>活跃用户数</p>
        </div>
        <div class="stat-card">
            <h3>${personaStats.total_memories || 0}</h3>
            <p>个人记忆总数</p>
        </div>
        <div class="stat-card">
            <h3>${(userStats.average_importance || 0).toFixed(1)}</h3>
            <p>平均重要性</p>
        </div>
    `;
}

// 加载用户列表
async function loadUsers() {
    try {
        // 这里需要一个获取用户列表的API，暂时使用模拟数据
        users = ['user1', 'user2', 'test_user', 'demo_user'];
        
        const userSelect = document.getElementById('user-select');
        userSelect.innerHTML = '<option value="">请选择用户</option>';
        
        users.forEach(user => {
            const option = document.createElement('option');
            option.value = user;
            option.textContent = user;
            userSelect.appendChild(option);
        });
    } catch (error) {
        console.error('加载用户列表失败:', error);
    }
}

// 加载用户记忆
async function loadUserMemories() {
    const userSelect = document.getElementById('user-select');
    currentUser = userSelect.value;
    
    if (!currentUser) {
        document.getElementById('user-memories-list').innerHTML = 
            '<div class="loading">请选择用户查看记忆</div>';
        return;
    }
    
    document.getElementById('user-memories-list').innerHTML = 
        '<div class="loading">正在加载用户记忆...</div>';
    
    try {
        const response = await fetch(`/api/memories/search?user_id=${currentUser}&limit=50`);
        const data = await response.json();
        
        if (data.success) {
            displayUserMemories(data.memories);
        } else {
            showError('user-memories-error', '加载用户记忆失败: ' + data.error);
        }
    } catch (error) {
        showError('user-memories-error', '网络错误: ' + error.message);
    }
}

// 搜索用户记忆
async function searchUserMemories() {
    if (!currentUser) {
        alert('请先选择用户');
        return;
    }
    
    const query = document.getElementById('user-search').value.trim();
    
    document.getElementById('user-memories-list').innerHTML = 
        '<div class="loading">正在搜索...</div>';
    
    try {
        const response = await fetch(`/api/memories/search?user_id=${currentUser}&query=${encodeURIComponent(query)}&limit=50`);
        const data = await response.json();
        
        if (data.success) {
            displayUserMemories(data.memories);
        } else {
            showError('user-memories-error', '搜索失败: ' + data.error);
        }
    } catch (error) {
        showError('user-memories-error', '网络错误: ' + error.message);
    }
}

// 显示用户记忆
function displayUserMemories(memories) {
    const container = document.getElementById('user-memories-list');
    
    if (!memories || memories.length === 0) {
        container.innerHTML = '<div class="loading">暂无记忆数据</div>';
        return;
    }
    
    container.innerHTML = memories.map(memory => `
        <div class="memory-item">
            <div class="memory-header">
                <span class="memory-type">${memory.type || '未知'}</span>
                <span class="memory-importance">重要性: ${(memory.importance || 0).toFixed(1)}</span>
            </div>
            <div class="memory-content">${memory.content}</div>
            <div class="memory-meta">
                <span>创建时间: ${formatDate(memory.created_at)}</span>
                <span>召回次数: ${memory.recall_count || 0}</span>
                ${memory.similarity ? `<span>相似度: ${(memory.similarity * 100).toFixed(1)}%</span>` : ''}
            </div>
        </div>
    `).join('');
}

// 清除用户记忆
async function clearUserMemories() {
    if (!currentUser) {
        alert('请先选择用户');
        return;
    }
    
    if (!confirm(`确定要清除用户 ${currentUser} 的所有记忆吗？此操作不可撤销！`)) {
        return;
    }
    
    try {
        const response = await fetch(`/api/memories/clear/${currentUser}`, {
            method: 'DELETE'
        });
        const data = await response.json();
        
        if (data.success) {
            alert('用户记忆已清除');
            loadUserMemories(); // 重新加载
            loadStats(); // 更新统计
        } else {
            alert('清除失败: ' + data.message);
        }
    } catch (error) {
        alert('网络错误: ' + error.message);
    }
}

// 加载个人记忆
async function loadPersonaMemories() {
    document.getElementById('persona-memories-list').innerHTML = 
        '<div class="loading">正在加载个人记忆...</div>';
    
    try {
        const response = await fetch('/api/memories/persona?limit=20');
        const data = await response.json();
        
        if (data.success) {
            displayPersonaMemories(data.memories);
        } else {
            showError('persona-memories-error', '加载个人记忆失败: ' + data.error);
        }
    } catch (error) {
        showError('persona-memories-error', '网络错误: ' + error.message);
    }
}

// 搜索个人记忆
async function searchPersonaMemories() {
    const query = document.getElementById('persona-search').value.trim();
    
    document.getElementById('persona-memories-list').innerHTML = 
        '<div class="loading">正在搜索...</div>';
    
    try {
        const response = await fetch(`/api/memories/persona?query=${encodeURIComponent(query)}&limit=20`);
        const data = await response.json();
        
        if (data.success) {
            displayPersonaMemories(data.memories);
        } else {
            showError('persona-memories-error', '搜索失败: ' + data.error);
        }
    } catch (error) {
        showError('persona-memories-error', '网络错误: ' + error.message);
    }
}

// 显示个人记忆
function displayPersonaMemories(memories) {
    const container = document.getElementById('persona-memories-list');
    
    if (!memories || memories.length === 0) {
        container.innerHTML = '<div class="loading">暂无记忆数据</div>';
        return;
    }
    
    container.innerHTML = memories.map(memory => `
        <div class="memory-item">
            <div class="memory-header">
                <span class="memory-type">${memory.memory_type || '未知'}</span>
                <span class="memory-importance">分享等级: ${memory.sharing_level || 1}</span>
            </div>
            <div class="memory-content">
                <strong>${memory.title || '无标题'}</strong><br>
                ${memory.content}
            </div>
            <div class="memory-meta">
                <span>类别: ${memory.category || '未知'}</span>
                <span>情感: ${memory.emotion || '中性'}</span>
                <span>时期: ${memory.time_period || '未知'}</span>
                ${memory.similarity ? `<span>相似度: ${(memory.similarity * 100).toFixed(1)}%</span>` : ''}
            </div>
        </div>
    `).join('');
}

// 显示错误信息
function showError(containerId, message) {
    const container = document.getElementById(containerId);
    if (container.classList.contains('error')) {
        container.innerHTML = message;
        container.style.display = 'block';
    } else {
        container.innerHTML = `<div class="error">${message}</div>`;
    }
}

// 格式化日期
function formatDate(dateString) {
    if (!dateString) return '未知';
    
    try {
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN');
    } catch (error) {
        return dateString;
    }
}

// 回车键搜索
document.addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        if (e.target.id === 'user-search') {
            searchUserMemories();
        } else if (e.target.id === 'persona-search') {
            searchPersonaMemories();
        }
    }
});
