class ChatApp {
    constructor() {
        this.userId = localStorage.getItem('userId') || this.generateUserId();
        this.sessionToken = localStorage.getItem('sessionToken');
        this.userInfo = this.getUserInfo();
        this.isLoading = false;
        this.init();
    }

    generateUserId() {
        const userId = 'user_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
        localStorage.setItem('userId', userId);
        return userId;
    }

    getUserInfo() {
        const userInfoStr = localStorage.getItem('userInfo');
        if (userInfoStr) {
            try {
                return JSON.parse(userInfoStr);
            } catch (e) {
                console.error('解析用户信息失败:', e);
                return null;
            }
        }
        return null;
    }

    init() {
        this.bindEvents();
        this.updateUserInterface();
        this.loadGreeting();
        this.updateCharCount();
    }

    bindEvents() {
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');

        // 发送按钮点击事件
        sendButton.addEventListener('click', () => this.sendMessage());

        // 输入框回车事件
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        // 字符计数
        messageInput.addEventListener('input', () => this.updateCharCount());
    }

    updateCharCount() {
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');

        // 根据输入内容启用/禁用发送按钮
        if (messageInput.value.trim()) {
            sendButton.disabled = false;
        } else {
            sendButton.disabled = true;
        }
    }

    updateUserInterface() {
        const userDisplay = document.getElementById('userDisplay');
        const contactStatus = document.getElementById('contactStatus');

        if (this.userInfo && this.sessionToken) {
            // 显示用户信息
            userDisplay.textContent = this.userInfo.nickname || this.userInfo.username || '用户';
            contactStatus.textContent = '在线';
        } else {
            // 显示游客信息
            userDisplay.textContent = '游客模式';
            contactStatus.textContent = '在线';
        }
    }

    async loadGreeting() {
        try {
            const response = await fetch('/api/greeting', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    user_id: this.userId
                })
            });

            const data = await response.json();

            if (data.success) {
                // 清除默认消息，保留系统消息
                const chatMessages = document.getElementById('chatMessages');
                const systemMessage = chatMessages.querySelector('.system-message');
                chatMessages.innerHTML = '';
                if (systemMessage) {
                    chatMessages.appendChild(systemMessage);
                }

                // 添加问候消息
                this.addMessage(data.response, 'assistant', new Date());
                this.updateStatus(data.affection_level, data.persona_activity);
            }
        } catch (error) {
            console.error('加载问候消息失败:', error);
        }
    }

    async sendMessage() {
        if (this.isLoading) return;

        const messageInput = document.getElementById('messageInput');
        const message = messageInput.value.trim();

        if (!message) {
            this.showError('请输入消息内容');
            return;
        }

        // 添加用户消息到界面
        this.addMessage(message, 'user', new Date());
        messageInput.value = '';
        this.updateCharCount();

        // 显示思考状态（打字指示器）
        this.showTypingIndicator();
        this.setLoading(true);

        try {
            const response = await fetch('/api/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    user_id: this.userId,
                    message: message
                })
            });

            const data = await response.json();

            if (data.success) {
                // 检查是否有分段消息
                if (data.response_segments && data.response_segments.length > 1) {
                    // 分段显示消息（会自动处理打字指示器）
                    this.addSegmentedMessages(data.response_segments, new Date(data.timestamp));
                } else {
                    // 单条消息：先隐藏打字指示器，再显示消息
                    this.hideTypingIndicator();
                    await this.delay(200); // 短暂停顿
                    this.addMessage(data.response, 'assistant', new Date(data.timestamp));
                }

                // 更新状态信息
                this.updateStatus(data.affection_level, data.persona_activity);

                // 显示好感度变化
                if (data.affection_change !== 0) {
                    this.showAffectionChange(data.affection_change);
                }
            } else {
                this.hideTypingIndicator();
                this.showError(data.error || '发送消息失败');
            }
        } catch (error) {
            console.error('发送消息失败:', error);
            this.hideTypingIndicator();
            this.showError('网络连接失败，请检查服务器状态');
        } finally {
            this.setLoading(false);
        }
    }

    addMessage(text, sender, timestamp) {
        const chatMessages = document.getElementById('chatMessages');

        // 创建消息组
        const messageGroup = document.createElement('div');
        messageGroup.className = 'message-group';

        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;

        const timeStr = this.formatTime(timestamp);

        if (sender === 'user') {
            // 用户消息：绿色气泡，右对齐，简单头像
            messageDiv.innerHTML = `
                <div class="message-avatar">
                    <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='%23667eea'/%3E%3Ctext x='20' y='26' text-anchor='middle' fill='white' font-size='16'%3E👤%3C/text%3E%3C/svg%3E" alt="用户">
                </div>
                <div class="message-content">
                    <div class="message-bubble">
                        <div class="message-text">${this.escapeHtml(text)}</div>
                    </div>
                    <div class="message-time">${timeStr}</div>
                </div>
            `;
        } else {
            // 助手消息：白色气泡，左对齐，小雨头像
            messageDiv.innerHTML = `
                <div class="message-avatar">
                    <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='%23ff9a9e'/%3E%3Ctext x='20' y='26' text-anchor='middle' fill='white' font-size='16'%3E🌸%3C/text%3E%3C/svg%3E" alt="小雨">
                </div>
                <div class="message-content">
                    <div class="message-bubble">
                        <div class="message-text">${this.escapeHtml(text)}</div>
                    </div>
                    <div class="message-time">${timeStr}</div>
                </div>
            `;
        }

        messageGroup.appendChild(messageDiv);
        chatMessages.appendChild(messageGroup);
        this.scrollToBottom();
    }

    async addSegmentedMessages(segments, timestamp) {
        /**
         * 分段显示消息，模拟微信聊天的多条消息效果
         * @param {Array} segments - 消息段落数组
         * @param {Date} timestamp - 时间戳
         */
        for (let i = 0; i < segments.length; i++) {
            const segment = segments[i];

            // 计算延迟时间：根据消息长度和是否是第一条消息
            let delay;
            if (i === 0) {
                // 第一条消息延迟稍长，模拟思考时间（打字指示器已经在显示）
                delay = Math.max(800, segment.length * 50);
            } else {
                // 后续消息延迟较短，模拟快速输入
                delay = Math.max(300, segment.length * 30);
                // 为后续消息重新显示打字指示器
                this.showTypingIndicator();
            }

            await this.delay(delay);

            // 隐藏打字指示器
            this.hideTypingIndicator();

            // 短暂停顿后显示消息
            await this.delay(200);

            // 添加消息
            this.addMessage(segment, 'assistant', timestamp);

            // 消息间的停顿（除了最后一条）
            if (i < segments.length - 1) {
                await this.delay(300);
            }
        }
    }

    showTypingIndicator() {
        /**
         * 显示打字指示器
         */
        const chatMessages = document.getElementById('chatMessages');

        // 检查是否已经有打字指示器
        if (document.getElementById('typingIndicator')) {
            return;
        }

        const typingGroup = document.createElement('div');
        typingGroup.className = 'message-group';

        const typingDiv = document.createElement('div');
        typingDiv.id = 'typingIndicator';
        typingDiv.className = 'message assistant-message typing-indicator';

        typingDiv.innerHTML = `
            <div class="message-avatar">
                <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='%23ff9a9e'/%3E%3Ctext x='20' y='26' text-anchor='middle' fill='white' font-size='16'%3E🌸%3C/text%3E%3C/svg%3E" alt="小雨">
            </div>
            <div class="message-content">
                <div class="message-bubble">
                    <div class="typing-dots">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                </div>
            </div>
        `;

        typingGroup.appendChild(typingDiv);
        chatMessages.appendChild(typingGroup);
        this.scrollToBottom();
    }

    hideTypingIndicator() {
        /**
         * 隐藏打字指示器
         */
        const typingIndicator = document.getElementById('typingIndicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }

    delay(ms) {
        /**
         * 延迟函数
         * @param {number} ms - 延迟毫秒数
         * @returns {Promise}
         */
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    updateStatus(affectionLevel, personaActivity) {
        // 更新好感度
        const affectionValue = document.getElementById('affectionValue');
        const affectionProgress = document.getElementById('affectionProgress');

        affectionValue.textContent = affectionLevel;
        affectionProgress.style.width = `${affectionLevel}%`;

        // 更新当前活动
        if (personaActivity) {
            const currentActivity = document.getElementById('currentActivity');
            currentActivity.textContent = personaActivity.activity || '休息中';
        }
    }

    showAffectionChange(change) {
        const changeText = change > 0 ? `+${change}` : `${change}`;
        const changeColor = change > 0 ? '#4CAF50' : '#f44336';

        // 创建临时提示
        const toast = document.createElement('div');
        toast.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            background: ${changeColor};
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            z-index: 1000;
            animation: slideIn 0.3s ease;
        `;
        toast.textContent = `好感度 ${changeText}`;

        document.body.appendChild(toast);

        setTimeout(() => {
            toast.remove();
        }, 2000);
    }

    setLoading(loading) {
        this.isLoading = loading;
        const sendButton = document.getElementById('sendButton');
        const messageInput = document.getElementById('messageInput');

        // 控制发送按钮和输入框的状态
        sendButton.disabled = loading || !messageInput.value.trim();
        messageInput.disabled = loading;
    }

    showError(message) {
        const errorToast = document.getElementById('errorToast');
        const errorMessage = document.getElementById('errorMessage');

        errorMessage.textContent = message;
        errorToast.style.display = 'flex';

        // 3秒后自动隐藏
        setTimeout(() => {
            this.hideError();
        }, 3000);
    }

    hideError() {
        const errorToast = document.getElementById('errorToast');
        errorToast.style.display = 'none';
    }

    scrollToBottom() {
        const chatMessages = document.getElementById('chatMessages');
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    formatTime(date) {
        const now = new Date();
        const messageDate = new Date(date);

        if (now.toDateString() === messageDate.toDateString()) {
            return messageDate.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });
        } else {
            return messageDate.toLocaleDateString('zh-CN', {
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// 微信风格的全局函数
function hideError() {
    const errorToast = document.getElementById('errorToast');
    errorToast.style.display = 'none';
}

function showChatList() {
    // 模拟返回聊天列表
    alert('返回聊天列表功能开发中...');
}

function showMoreOptions() {
    // 显示状态面板
    const statusPanel = document.getElementById('statusPanel');
    statusPanel.classList.add('show');
}

function hideStatusPanel() {
    const statusPanel = document.getElementById('statusPanel');
    statusPanel.classList.remove('show');
}

function showUserProfile() {
    alert('用户资料功能开发中...');
}

function logout() {
    if (confirm('确定要登出吗？')) {
        // 清除本地存储
        localStorage.removeItem('sessionToken');
        localStorage.removeItem('userInfo');

        // 重新加载页面
        window.location.reload();
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new ChatApp();
});

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
`;
document.head.appendChild(style);
