* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    background: #f7f7f7;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0;
    margin: 0;
}

/* 微信容器 */
.wechat-container {
    width: 100%;
    max-width: 414px;
    height: 100vh;
    background: #f7f7f7;
    display: flex;
    flex-direction: column;
    position: relative;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
}

/* 微信头部导航栏 */
.wechat-header {
    background: #393a3e;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    color: white;
    position: relative;
    z-index: 100;
}

.header-left, .header-right {
    width: 60px;
    display: flex;
    justify-content: center;
}

.header-center {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
}

.back-btn, .more-btn {
    background: none;
    border: none;
    color: white;
    padding: 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.2s;
}

.back-btn:hover, .more-btn:hover {
    background: rgba(255,255,255,0.1);
}

.contact-info {
    text-align: center;
}

.contact-name {
    font-size: 18px;
    font-weight: 500;
    color: white;
    margin-bottom: 2px;
}

.contact-status {
    font-size: 12px;
    color: rgba(255,255,255,0.7);
}

/* 微信消息区域 */
.wechat-messages {
    flex: 1;
    overflow-y: auto;
    background: #f7f7f7;
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

/* 系统消息 */
.system-message {
    display: flex;
    justify-content: center;
    margin: 16px 0;
}

.system-text {
    background: rgba(0,0,0,0.1);
    color: #999;
    font-size: 12px;
    padding: 4px 12px;
    border-radius: 12px;
}

/* 消息组 */
.message-group {
    margin-bottom: 8px;
}

.message {
    display: flex;
    margin-bottom: 8px;
    max-width: 80%;
}

.user-message {
    align-self: flex-end;
    flex-direction: row-reverse;
    margin-left: auto;
}

.assistant-message {
    align-self: flex-start;
    margin-right: auto;
}

/* 头像样式 */
.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 6px;
    flex-shrink: 0;
    margin: 0 8px;
    overflow: hidden;
}

.message-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* 消息内容 */
.message-content {
    display: flex;
    flex-direction: column;
    max-width: calc(100% - 56px);
}

.message-bubble {
    position: relative;
    word-wrap: break-word;
    word-break: break-word;
}

.message-text {
    padding: 10px 16px;
    line-height: 1.4;
    font-size: 16px;
    border-radius: 8px;
    position: relative;
}

/* 用户消息气泡 */
.user-message .message-text {
    background: #95ec69;
    color: #000;
    border-radius: 8px 8px 2px 8px;
}

/* 助手消息气泡 */
.assistant-message .message-text {
    background: white;
    color: #000;
    border-radius: 8px 8px 8px 2px;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

/* 消息时间 */
.message-time {
    font-size: 11px;
    color: #999;
    margin-top: 4px;
    padding: 0 4px;
}

.user-message .message-time {
    text-align: right;
}

/* 微信输入区域 */
.wechat-input {
    background: #f7f7f7;
    border-top: 1px solid #e5e5e5;
    padding: 8px 16px 8px 16px;
    padding-bottom: calc(8px + env(safe-area-inset-bottom));
}

.input-toolbar {
    display: flex;
    align-items: center;
    gap: 8px;
    min-height: 48px;
}

.toolbar-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: none;
    color: #666;
    cursor: pointer;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s;
    flex-shrink: 0;
}

.toolbar-btn:hover {
    background: rgba(0,0,0,0.05);
}

.toolbar-btn:active {
    background: rgba(0,0,0,0.1);
}

.input-area {
    flex: 1;
    margin: 0 8px;
}

.message-input {
    width: 100%;
    min-height: 36px;
    max-height: 120px;
    padding: 8px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    font-size: 16px;
    line-height: 1.4;
    background: white;
    outline: none;
    resize: none;
    transition: border-color 0.2s;
}

.message-input:focus {
    border-color: #07c160;
}

.send-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: #07c160;
    color: white;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s;
    flex-shrink: 0;
}

.send-btn:hover:not(:disabled) {
    background: #06ad56;
}

.send-btn:disabled {
    background: #c9c9c9;
    cursor: not-allowed;
}



/* 状态面板 */
.status-panel {
    position: fixed;
    top: 0;
    right: -300px;
    width: 300px;
    height: 100vh;
    background: white;
    box-shadow: -2px 0 8px rgba(0,0,0,0.1);
    transition: right 0.3s ease;
    z-index: 200;
    display: flex;
    flex-direction: column;
}

.status-panel.show {
    right: 0;
}

.status-header {
    background: #393a3e;
    color: white;
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 500;
}

.close-panel {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    padding: 4px;
}

.status-content {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
}

.status-item {
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;
}

.status-item:last-child {
    border-bottom: none;
}

.status-label {
    display: block;
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
}

.affection-display {
    display: flex;
    align-items: center;
    gap: 8px;
}

.affection-bar {
    flex: 1;
    height: 6px;
    background: #f0f0f0;
    border-radius: 3px;
    overflow: hidden;
}

.affection-progress {
    height: 100%;
    background: #07c160;
    border-radius: 3px;
    transition: width 0.3s ease;
    width: 30%;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.login-link {
    color: #07c160;
    text-decoration: none;
    font-size: 14px;
    padding: 4px 8px;
    border: 1px solid #07c160;
    border-radius: 4px;
    transition: all 0.2s;
}

.login-link:hover {
    background: #07c160;
    color: white;
}

/* 打字指示器样式 */
.typing-indicator {
    opacity: 0.8;
}

.typing-dots {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 10px 16px;
    background: white;
    border-radius: 8px 8px 8px 2px;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    min-height: 20px;
}

.typing-dots span {
    width: 6px;
    height: 6px;
    background: #999;
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: 0s; }
.typing-dots span:nth-child(2) { animation-delay: 0.2s; }
.typing-dots span:nth-child(3) { animation-delay: 0.4s; }

@keyframes typing {
    0%, 60%, 100% {
        transform: translateY(0);
        opacity: 0.4;
    }
    30% {
        transform: translateY(-6px);
        opacity: 1;
    }
}

/* 错误提示 */
.error-toast {
    position: fixed;
    top: 80px;
    left: 50%;
    transform: translateX(-50%);
    background: #ff4757;
    color: white;
    padding: 12px 16px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 10px;
    box-shadow: 0 4px 12px rgba(255,71,87,0.3);
    z-index: 1000;
    max-width: 90%;
}

.error-toast button {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 响应式设计 */
@media (max-width: 480px) {
    body {
        align-items: stretch;
    }

    .wechat-container {
        max-width: 100%;
        height: 100vh;
        border-radius: 0;
        box-shadow: none;
    }

    .status-panel {
        width: 100%;
        right: -100%;
    }

    .message {
        max-width: 85%;
    }

    .message-text {
        font-size: 15px;
    }
}

@media (min-width: 481px) {
    body {
        padding: 20px;
    }

    .wechat-container {
        border-radius: 12px;
        height: calc(100vh - 40px);
        max-height: 800px;
    }
}

/* 滚动条样式 */
.wechat-messages::-webkit-scrollbar {
    width: 4px;
}

.wechat-messages::-webkit-scrollbar-track {
    background: transparent;
}

.wechat-messages::-webkit-scrollbar-thumb {
    background: rgba(0,0,0,0.2);
    border-radius: 2px;
}

.wechat-messages::-webkit-scrollbar-thumb:hover {
    background: rgba(0,0,0,0.3);
}

/* 状态面板滚动条 */
.status-content::-webkit-scrollbar {
    width: 4px;
}

.status-content::-webkit-scrollbar-track {
    background: #f0f0f0;
}

.status-content::-webkit-scrollbar-thumb {
    background: #c0c0c0;
    border-radius: 2px;
}

/* 动画效果 */
.message {
    animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 工具栏按钮激活状态 */
.toolbar-btn.active {
    background: rgba(7,193,96,0.1);
    color: #07c160;
}
