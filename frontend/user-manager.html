<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理 - 虚拟人恋爱陪伴系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 100%;
            max-width: 400px;
            margin: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .content {
            padding: 30px 20px;
        }

        .tab-buttons {
            display: flex;
            margin-bottom: 30px;
            background: #f5f5f5;
            border-radius: 10px;
            padding: 5px;
        }

        .tab-button {
            flex: 1;
            padding: 12px;
            border: none;
            background: transparent;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .tab-button.active {
            background: white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            color: #667eea;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }

        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .form-group input.optional {
            border-color: #f0f0f0;
        }

        .submit-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
        }

        .submit-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .message {
            padding: 12px 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .guest-mode {
            text-align: center;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e1e5e9;
        }

        .guest-btn {
            color: #667eea;
            text-decoration: none;
            font-size: 14px;
            padding: 10px 20px;
            border: 1px solid #667eea;
            border-radius: 20px;
            display: inline-block;
            transition: all 0.3s ease;
        }

        .guest-btn:hover {
            background: #667eea;
            color: white;
        }

        .optional-label {
            color: #999;
            font-size: 12px;
            margin-left: 5px;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌸 小雨</h1>
            <p>虚拟人恋爱陪伴系统</p>
        </div>
        
        <div class="content">
            <div id="message" class="message" style="display: none;"></div>
            
            <div class="tab-buttons">
                <button class="tab-button active" onclick="switchTab('login')">登录</button>
                <button class="tab-button" onclick="switchTab('register')">注册</button>
            </div>
            
            <!-- 登录表单 -->
            <div id="login-tab" class="tab-content active">
                <form id="loginForm">
                    <div class="form-group">
                        <label for="loginUsername">用户名</label>
                        <input type="text" id="loginUsername" name="username" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="loginPassword">密码</label>
                        <input type="password" id="loginPassword" name="password" required>
                    </div>
                    
                    <button type="submit" class="submit-btn" id="loginBtn">
                        登录
                    </button>
                </form>
            </div>
            
            <!-- 注册表单 -->
            <div id="register-tab" class="tab-content">
                <form id="registerForm">
                    <div class="form-group">
                        <label for="registerUsername">用户名</label>
                        <input type="text" id="registerUsername" name="username" required 
                               placeholder="3-20个字符，只能包含字母、数字、下划线">
                    </div>
                    
                    <div class="form-group">
                        <label for="registerPassword">密码</label>
                        <input type="password" id="registerPassword" name="password" required 
                               placeholder="至少6个字符">
                    </div>
                    
                    <div class="form-group">
                        <label for="registerNickname">
                            昵称
                            <span class="optional-label">(可选)</span>
                        </label>
                        <input type="text" id="registerNickname" name="nickname" class="optional" 
                               placeholder="显示名称">
                    </div>
                    
                    <div class="form-group">
                        <label for="registerEmail">
                            邮箱
                            <span class="optional-label">(可选)</span>
                        </label>
                        <input type="email" id="registerEmail" name="email" class="optional" 
                               placeholder="用于找回密码">
                    </div>
                    
                    <button type="submit" class="submit-btn" id="registerBtn">
                        注册
                    </button>
                </form>
            </div>
            
            <!-- 游客模式 -->
            <div class="guest-mode">
                <p style="color: #666; margin-bottom: 10px;">或者</p>
                <a href="/" class="guest-btn">游客模式体验</a>
            </div>
        </div>
    </div>

    <script>
        class UserManager {
            constructor() {
                this.init();
            }

            init() {
                this.bindEvents();
                this.checkExistingSession();
            }

            bindEvents() {
                document.getElementById('loginForm').addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.handleLogin();
                });

                document.getElementById('registerForm').addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.handleRegister();
                });
            }

            checkExistingSession() {
                const sessionToken = localStorage.getItem('sessionToken');
                if (sessionToken) {
                    // 如果已有会话，直接跳转到聊天页面
                    window.location.href = '/';
                }
            }

            async handleLogin() {
                const form = document.getElementById('loginForm');
                const formData = new FormData(form);
                const data = Object.fromEntries(formData);

                this.setLoading('loginBtn', true);
                this.hideMessage();

                try {
                    const response = await fetch('/api/users/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(data)
                    });

                    const result = await response.json();

                    if (result.success) {
                        // 保存会话令牌和用户信息
                        localStorage.setItem('sessionToken', result.session_token);
                        localStorage.setItem('userInfo', JSON.stringify(result.user));
                        localStorage.setItem('userId', result.user.user_id);

                        this.showMessage('登录成功！正在跳转...', 'success');
                        
                        // 跳转到聊天页面
                        setTimeout(() => {
                            window.location.href = '/';
                        }, 1000);
                    } else {
                        this.showMessage(result.error || '登录失败', 'error');
                    }
                } catch (error) {
                    console.error('Login error:', error);
                    this.showMessage('网络错误，请稍后重试', 'error');
                } finally {
                    this.setLoading('loginBtn', false);
                }
            }

            async handleRegister() {
                const form = document.getElementById('registerForm');
                const formData = new FormData(form);
                const data = Object.fromEntries(formData);

                // 移除空值
                Object.keys(data).forEach(key => {
                    if (!data[key]) {
                        delete data[key];
                    }
                });

                this.setLoading('registerBtn', true);
                this.hideMessage();

                try {
                    const response = await fetch('/api/users/register', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(data)
                    });

                    const result = await response.json();

                    if (result.success) {
                        this.showMessage('注册成功！请登录', 'success');
                        
                        // 切换到登录标签页
                        setTimeout(() => {
                            switchTab('login');
                            // 预填用户名
                            document.getElementById('loginUsername').value = data.username;
                        }, 1000);
                    } else {
                        this.showMessage(result.error || '注册失败', 'error');
                    }
                } catch (error) {
                    console.error('Register error:', error);
                    this.showMessage('网络错误，请稍后重试', 'error');
                } finally {
                    this.setLoading('registerBtn', false);
                }
            }

            setLoading(buttonId, loading) {
                const button = document.getElementById(buttonId);
                if (loading) {
                    button.disabled = true;
                    button.innerHTML = '<span class="loading"></span>处理中...';
                } else {
                    button.disabled = false;
                    button.innerHTML = buttonId === 'loginBtn' ? '登录' : '注册';
                }
            }

            showMessage(text, type) {
                const messageEl = document.getElementById('message');
                messageEl.textContent = text;
                messageEl.className = `message ${type}`;
                messageEl.style.display = 'block';
            }

            hideMessage() {
                const messageEl = document.getElementById('message');
                messageEl.style.display = 'none';
            }
        }

        function switchTab(tabName) {
            // 更新按钮状态
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            // 更新内容显示
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            document.getElementById(`${tabName}-tab`).classList.add('active');

            // 隐藏消息
            document.getElementById('message').style.display = 'none';
        }

        // 初始化
        new UserManager();
    </script>
</body>
</html>
